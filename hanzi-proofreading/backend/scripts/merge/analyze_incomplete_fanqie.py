#!/usr/bin/env python3
"""
analyze_incomplete_fanqie.py - 分析字形完整但反切读音不完整的情况

功能：
1. 分析3个源都有的字形，但反切值没有在3个源都有的情况
2. 统计各种不完整模式
3. 生成详细的分析报告（Markdown格式）

作者：AI Assistant
日期：2025-01-01
"""

import sys
import os
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Set, Optional, Tuple
from collections import defaultdict, Counter
from datetime import datetime
from tqdm import tqdm
import difflib

# 添加项目根目录到路径，以便导入app模块
sys.path.append(str(Path(__file__).parent.parent.parent))

from sqlalchemy.orm import Session
from sqlalchemy import create_engine, text
from app.database import SessionLocal, DATABASE_URL
from app import models, schemas, crud

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analyze_incomplete_fanqie.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class CharacterMapper:
    """字符映射器，用于处理简繁体和异体字"""

    def __init__(self):
        # 常见的简繁体和异体字映射
        self.char_mappings = {
            # 简繁体对应
            '没': '沒', '卧': '臥', '缘': '緣', '县': '縣', '兖': '兗', '缘': '縁',
            '卫': '衛', '卫': '衞', '恱': '悅', '芮': '芮', '甞': '嘗',
            '雪': '雪', '绢': '絹', '臥': '卧', '緣': '縁', '縣': '县',
            '兗': '兖', '縁': '缘', '衛': '卫', '衞': '卫', '悅': '恱',
            '嘗': '甞', '絹': '绢',
            # 异体字对应
            '衛': '衞', '衞': '衛', '恱': '悅', '悅': '恱',
            '甞': '嘗', '嘗': '甞', '縁': '緣', '緣': '縁',
        }

        # 构建双向映射
        self.bidirectional_mappings = {}
        for k, v in self.char_mappings.items():
            self.bidirectional_mappings[k] = v
            self.bidirectional_mappings[v] = k

    def normalize_char(self, char: str) -> str:
        """标准化字符，将异体字转换为标准形式"""
        return self.bidirectional_mappings.get(char, char)

    def normalize_fanqie(self, fanqie: str) -> str:
        """标准化反切"""
        if not fanqie or len(fanqie) < 3:
            return fanqie

        # 标准化每个字符
        normalized = ''.join(self.normalize_char(char) for char in fanqie)
        return normalized

    def are_similar_fanqie(self, fanqie1: str, fanqie2: str) -> Tuple[bool, List[Tuple[int, str, str]]]:
        """
        判断两个反切是否相似（仅一字之差）
        返回: (是否相似, 差异列表)
        """
        if not fanqie1 or not fanqie2:
            return False, []

        if len(fanqie1) != len(fanqie2):
            return False, []

        # 先尝试标准化比较
        norm1 = self.normalize_fanqie(fanqie1)
        norm2 = self.normalize_fanqie(fanqie2)

        if norm1 == norm2:
            return True, []

        # 逐字符比较
        differences = []
        for i, (c1, c2) in enumerate(zip(fanqie1, fanqie2)):
            if c1 != c2:
                differences.append((i, c1, c2))

        # 如果只有一个字符不同，且这个字符是已知的映射关系
        if len(differences) == 1:
            pos, char1, char2 = differences[0]
            if (char1 in self.bidirectional_mappings and
                self.bidirectional_mappings[char1] == char2) or \
               (char2 in self.bidirectional_mappings and
                self.bidirectional_mappings[char2] == char1):
                return True, differences

        return False, differences


class IncompleteFanqieAnalyzer:
    """不完整反切分析器"""
    
    def __init__(self):
        self.db = SessionLocal()
        self.char_mapper = CharacterMapper()
        self.stats = {
            'total_unicode_analyzed': 0,
            'unicode_with_all_sources': 0,
            'unicode_with_incomplete_fanqie': 0,
            'unicode_with_similar_fanqie': 0,
            'fanqie_patterns': defaultdict(int),  # 反切模式统计
            'source_combinations': defaultdict(int),  # 源组合统计
            'incomplete_details': [],  # 详细的不完整记录
            'field_completeness': defaultdict(lambda: defaultdict(int)),  # 字段完整性统计
            'similar_fanqie_groups': [],  # 相似反切组
            'character_variant_stats': defaultdict(int)  # 字符变体统计
        }
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.db.close()
    
    def get_all_unicode_list(self) -> List[str]:
        """获取所有unicode列表"""
        query = """
        SELECT DISTINCT unicode
        FROM yunshu_gy_origin
        WHERE unicode IS NOT NULL
        ORDER BY unicode
        """
        
        result = self.db.execute(text(query))
        unicode_list = [row[0] for row in result.fetchall()]
        
        logger.info(f"找到 {len(unicode_list)} 个unicode待分析")
        return unicode_list
    
    def get_records_by_unicode(self, unicode_code: str) -> List[models.YinyunGyOrigin]:
        """获取指定unicode的所有记录"""
        return self.db.query(models.YinyunGyOrigin).filter(
            models.YinyunGyOrigin.unicode == unicode_code
        ).order_by(
            text("CASE source WHEN 'xxt' THEN 1 WHEN 'qx' THEN 2 WHEN 'yd' THEN 3 END")
        ).all()
    
    def group_by_source(self, records: List[models.YinyunGyOrigin]) -> Dict[str, List[models.YinyunGyOrigin]]:
        """按来源分组"""
        sources = defaultdict(list)
        for record in records:
            sources[record.source].append(record)
        return dict(sources)
    
    def has_all_three_sources(self, sources: Dict[str, List[models.YinyunGyOrigin]]) -> bool:
        """检查是否包含所有3个源"""
        return {'xxt', 'qx', 'yd'}.issubset(sources.keys())
    
    def get_fanqie_by_source(self, sources: Dict[str, List[models.YinyunGyOrigin]]) -> Dict[str, Set[str]]:
        """获取每个源的反切值集合"""
        fanqie_by_source = {}
        for source, records in sources.items():
            fanqie_set = set()
            for record in records:
                if record.fan_qie and record.fan_qie.strip():
                    fanqie_set.add(record.fan_qie.strip())
            fanqie_by_source[source] = fanqie_set
        return fanqie_by_source
    
    def analyze_fanqie_completeness(self, fanqie_by_source: Dict[str, Set[str]]) -> Dict[str, any]:
        """分析反切完整性"""
        # 获取所有反切值
        all_fanqie = set()
        for fanqie_set in fanqie_by_source.values():
            all_fanqie.update(fanqie_set)
        
        # 分析每个反切值在哪些源中存在
        fanqie_analysis = {}
        for fanqie in all_fanqie:
            sources_with_fanqie = []
            for source, fanqie_set in fanqie_by_source.items():
                if fanqie in fanqie_set:
                    sources_with_fanqie.append(source)
            
            fanqie_analysis[fanqie] = {
                'sources': sources_with_fanqie,
                'source_count': len(sources_with_fanqie),
                'is_complete': len(sources_with_fanqie) == 3
            }
        
        return fanqie_analysis
    
    def get_field_completeness(self, sources: Dict[str, List[models.YinyunGyOrigin]]) -> Dict[str, Dict[str, int]]:
        """分析各字段的完整性"""
        fields = ['fan_qie', 'sheng_mu', 'yun_bu', 'sheng_diao', 'kai_he', 'deng_di', 'she', 'qing_zhuo', 'shi_yi', 'xiao_yun']
        field_completeness = {}
        
        for field in fields:
            field_stats = {'total_sources': 0, 'non_empty_sources': 0}
            for source, records in sources.items():
                field_stats['total_sources'] += 1
                has_value = False
                for record in records:
                    value = getattr(record, field, None)
                    if value and str(value).strip():
                        has_value = True
                        break
                if has_value:
                    field_stats['non_empty_sources'] += 1
            
            field_completeness[field] = field_stats
        
        return field_completeness

    def find_similar_fanqie_groups(self, fanqie_by_source: Dict[str, Set[str]]) -> List[Dict]:
        """找出相似的反切组（仅一字之差）"""
        all_fanqie = set()
        for fanqie_set in fanqie_by_source.values():
            all_fanqie.update(fanqie_set)

        all_fanqie = list(all_fanqie)
        similar_groups = []
        processed = set()

        for i, fanqie1 in enumerate(all_fanqie):
            if fanqie1 in processed:
                continue

            group = {
                'normalized_fanqie': self.char_mapper.normalize_fanqie(fanqie1),
                'variants': [fanqie1],
                'sources_by_variant': {fanqie1: []},
                'differences': []
            }

            # 找出fanqie1在哪些源中存在
            for source, fanqie_set in fanqie_by_source.items():
                if fanqie1 in fanqie_set:
                    group['sources_by_variant'][fanqie1].append(source)

            # 寻找相似的反切
            for j, fanqie2 in enumerate(all_fanqie[i+1:], i+1):
                if fanqie2 in processed:
                    continue

                is_similar, differences = self.char_mapper.are_similar_fanqie(fanqie1, fanqie2)
                if is_similar:
                    group['variants'].append(fanqie2)
                    group['sources_by_variant'][fanqie2] = []

                    # 找出fanqie2在哪些源中存在
                    for source, fanqie_set in fanqie_by_source.items():
                        if fanqie2 in fanqie_set:
                            group['sources_by_variant'][fanqie2].append(source)

                    if differences:
                        group['differences'].extend(differences)

                    processed.add(fanqie2)

            if len(group['variants']) > 1:
                similar_groups.append(group)
                processed.add(fanqie1)

        return similar_groups

    def analyze_unicode_data(self, unicode_code: str):
        """分析单个unicode的数据"""
        # 获取该unicode的所有记录
        records = self.get_records_by_unicode(unicode_code)
        if not records:
            return

        # 按来源分组
        sources = self.group_by_source(records)

        # 检查是否包含所有3个源
        if not self.has_all_three_sources(sources):
            return

        self.stats['unicode_with_all_sources'] += 1

        # 获取每个源的反切值
        fanqie_by_source = self.get_fanqie_by_source(sources)

        # 分析反切完整性
        fanqie_analysis = self.analyze_fanqie_completeness(fanqie_by_source)

        # 寻找相似的反切组
        similar_groups = self.find_similar_fanqie_groups(fanqie_by_source)

        # 检查是否有不完整的反切
        has_incomplete_fanqie = False
        incomplete_fanqie = []
        complete_fanqie = []
        has_similar_fanqie = len(similar_groups) > 0

        for fanqie, analysis in fanqie_analysis.items():
            if not analysis['is_complete']:
                has_incomplete_fanqie = True
                incomplete_fanqie.append({
                    'fanqie': fanqie,
                    'sources': analysis['sources'],
                    'source_count': analysis['source_count']
                })
            else:
                complete_fanqie.append(fanqie)

        # 统计相似反切
        if has_similar_fanqie:
            self.stats['unicode_with_similar_fanqie'] += 1

            # 记录相似反切组
            hanzi = records[0].hanzi if records else unicode_code
            for group in similar_groups:
                group_info = {
                    'unicode': unicode_code,
                    'hanzi': hanzi,
                    'normalized_fanqie': group['normalized_fanqie'],
                    'variants': group['variants'],
                    'sources_by_variant': group['sources_by_variant'],
                    'differences': group['differences']
                }
                self.stats['similar_fanqie_groups'].append(group_info)

                # 统计字符变体
                for diff in group['differences']:
                    pos, char1, char2 = diff
                    variant_pair = f"{char1}→{char2}"
                    self.stats['character_variant_stats'][variant_pair] += 1

        if has_incomplete_fanqie:
            self.stats['unicode_with_incomplete_fanqie'] += 1

            # 统计反切模式
            for incomplete in incomplete_fanqie:
                pattern = '+'.join(sorted(incomplete['sources']))
                self.stats['fanqie_patterns'][pattern] += 1
                self.stats['source_combinations'][incomplete['source_count']] += 1

            # 获取字段完整性
            field_completeness = self.get_field_completeness(sources)

            # 记录详细信息
            hanzi = records[0].hanzi if records else unicode_code
            detail = {
                'unicode': unicode_code,
                'hanzi': hanzi,
                'total_fanqie': len(fanqie_analysis),
                'complete_fanqie': complete_fanqie,
                'incomplete_fanqie': incomplete_fanqie,
                'fanqie_by_source': {k: list(v) for k, v in fanqie_by_source.items()},
                'field_completeness': field_completeness,
                'similar_fanqie_groups': similar_groups
            }
            self.stats['incomplete_details'].append(detail)

            # 更新字段完整性统计
            for field, stats in field_completeness.items():
                completeness_level = f"{stats['non_empty_sources']}/3"
                self.stats['field_completeness'][field][completeness_level] += 1

    def analyze_all_data(self, limit: Optional[int] = None):
        """分析所有数据"""
        logger.info("开始分析不完整反切数据...")

        # 获取unicode列表
        unicode_list = self.get_all_unicode_list()

        if limit:
            unicode_list = unicode_list[:limit]
            logger.info(f"限制分析数量为: {limit}")

        if not unicode_list:
            logger.warning("没有找到需要分析的数据")
            return

        # 分析每个unicode
        for unicode_code in tqdm(unicode_list, desc="分析进度"):
            try:
                self.analyze_unicode_data(unicode_code)
                self.stats['total_unicode_analyzed'] += 1
            except Exception as e:
                logger.error(f"分析Unicode {unicode_code} 时出错: {e}")
                continue

        # 输出统计信息
        self.print_statistics()

    def print_statistics(self):
        """输出分析统计信息"""
        logger.info("=" * 50)
        logger.info("分析统计信息:")
        logger.info(f"分析的Unicode总数: {self.stats['total_unicode_analyzed']}")
        logger.info(f"包含所有3个源的Unicode: {self.stats['unicode_with_all_sources']}")
        logger.info(f"存在不完整反切的Unicode: {self.stats['unicode_with_incomplete_fanqie']}")
        logger.info(f"存在相似反切的Unicode: {self.stats['unicode_with_similar_fanqie']}")
        logger.info(f"不完整比例: {self.stats['unicode_with_incomplete_fanqie']/self.stats['unicode_with_all_sources']*100:.2f}%" if self.stats['unicode_with_all_sources'] > 0 else "0%")
        logger.info(f"相似反切比例: {self.stats['unicode_with_similar_fanqie']/self.stats['unicode_with_all_sources']*100:.2f}%" if self.stats['unicode_with_all_sources'] > 0 else "0%")
        logger.info("反切模式分布:")
        for pattern, count in sorted(self.stats['fanqie_patterns'].items(), key=lambda x: x[1], reverse=True)[:10]:
            logger.info(f"  {pattern}: {count}")
        logger.info("字符变体统计:")
        for variant, count in sorted(self.stats['character_variant_stats'].items(), key=lambda x: x[1], reverse=True)[:10]:
            logger.info(f"  {variant}: {count}")
        logger.info("=" * 50)

    def generate_markdown_report(self, output_file: str = None):
        """生成Markdown格式的分析报告"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"incomplete_fanqie_analysis_{timestamp}.md"

        report_lines = []

        # 报告标题和概述
        report_lines.append("# 字形完整但反切读音不完整分析报告")
        report_lines.append("")
        report_lines.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        # 总体统计
        report_lines.append("## 总体统计")
        report_lines.append("")
        report_lines.append(f"- **分析的Unicode总数**: {self.stats['total_unicode_analyzed']:,}")
        report_lines.append(f"- **包含所有3个源的Unicode**: {self.stats['unicode_with_all_sources']:,}")
        report_lines.append(f"- **存在不完整反切的Unicode**: {self.stats['unicode_with_incomplete_fanqie']:,}")
        report_lines.append(f"- **存在相似反切的Unicode**: {self.stats['unicode_with_similar_fanqie']:,}")

        if self.stats['unicode_with_all_sources'] > 0:
            incomplete_ratio = self.stats['unicode_with_incomplete_fanqie'] / self.stats['unicode_with_all_sources'] * 100
            similar_ratio = self.stats['unicode_with_similar_fanqie'] / self.stats['unicode_with_all_sources'] * 100
            report_lines.append(f"- **不完整比例**: {incomplete_ratio:.2f}%")
            report_lines.append(f"- **相似反切比例**: {similar_ratio:.2f}%")

        report_lines.append("")

        # 反切模式分布
        report_lines.append("## 反切模式分布")
        report_lines.append("")
        report_lines.append("按源组合统计不完整反切的分布模式：")
        report_lines.append("")
        report_lines.append("| 源组合 | 数量 | 占比 |")
        report_lines.append("|--------|------|------|")

        total_patterns = sum(self.stats['fanqie_patterns'].values())
        for pattern, count in sorted(self.stats['fanqie_patterns'].items(), key=lambda x: x[1], reverse=True):
            percentage = count / total_patterns * 100 if total_patterns > 0 else 0
            report_lines.append(f"| {pattern} | {count:,} | {percentage:.2f}% |")

        report_lines.append("")

        # 源数量分布
        report_lines.append("## 源数量分布")
        report_lines.append("")
        report_lines.append("按反切值存在的源数量统计：")
        report_lines.append("")
        report_lines.append("| 源数量 | 反切值数量 | 说明 |")
        report_lines.append("|--------|------------|------|")

        for source_count in sorted(self.stats['source_combinations'].keys()):
            count = self.stats['source_combinations'][source_count]
            description = "完整" if source_count == 3 else "不完整"
            report_lines.append(f"| {source_count} | {count:,} | {description} |")

        report_lines.append("")

        # 相似反切分析（字符变体）
        report_lines.append("## 相似反切分析（字符变体）")
        report_lines.append("")
        report_lines.append("分析发现的仅一字之差的反切音，通常是简繁体或异体字差异：")
        report_lines.append("")

        # 字符变体统计
        report_lines.append("### 字符变体统计")
        report_lines.append("")
        report_lines.append("| 字符变体 | 出现次数 | 说明 |")
        report_lines.append("|----------|----------|------|")

        for variant, count in sorted(self.stats['character_variant_stats'].items(),
                                   key=lambda x: x[1], reverse=True)[:20]:
            char1, char2 = variant.split('→')
            description = "简繁体" if char1 in self.char_mapper.bidirectional_mappings else "异体字"
            report_lines.append(f"| {variant} | {count:,} | {description} |")

        report_lines.append("")

        # 相似反切组示例
        report_lines.append("### 相似反切组示例")
        report_lines.append("")
        report_lines.append("以下是一些典型的相似反切组（仅字符变体差异）：")
        report_lines.append("")

        # 选择前10个相似反切组作为示例
        similar_examples = self.stats['similar_fanqie_groups'][:10]
        for i, group in enumerate(similar_examples, 1):
            report_lines.append(f"#### 示例 {i}: {group['hanzi']} (U+{group['unicode']})")
            report_lines.append("")
            report_lines.append(f"- **标准化反切**: {group['normalized_fanqie']}")
            report_lines.append("- **变体形式**:")

            for variant in group['variants']:
                sources = '+'.join(group['sources_by_variant'][variant])
                report_lines.append(f"  - `{variant}` (在 {sources} 中)")

            if group['differences']:
                report_lines.append("- **字符差异**:")
                for pos, char1, char2 in group['differences']:
                    report_lines.append(f"  - 位置 {pos}: `{char1}` ↔ `{char2}`")

            report_lines.append("")

        report_lines.append("")

        # 字段完整性分析
        report_lines.append("## 字段完整性分析")
        report_lines.append("")
        report_lines.append("各字段在不完整反切记录中的完整性统计：")
        report_lines.append("")

        field_names = {
            'fan_qie': '反切',
            'sheng_mu': '声母',
            'yun_bu': '韵部',
            'sheng_diao': '声调',
            'kai_he': '开合',
            'deng_di': '等第',
            'she': '摄',
            'qing_zhuo': '清浊',
            'shi_yi': '释义',
            'xiao_yun': '小韵'
        }

        for field, display_name in field_names.items():
            if field in self.stats['field_completeness']:
                report_lines.append(f"### {display_name} ({field})")
                report_lines.append("")
                report_lines.append("| 完整性 | 记录数 |")
                report_lines.append("|--------|--------|")

                field_stats = self.stats['field_completeness'][field]
                for completeness, count in sorted(field_stats.items()):
                    report_lines.append(f"| {completeness} | {count:,} |")

                report_lines.append("")

        # 详细示例
        report_lines.append("## 详细示例")
        report_lines.append("")
        report_lines.append("以下是一些典型的不完整反切示例：")
        report_lines.append("")

        # 选择前20个示例
        examples = sorted(self.stats['incomplete_details'],
                         key=lambda x: len(x['incomplete_fanqie']),
                         reverse=True)[:20]

        for i, example in enumerate(examples, 1):
            report_lines.append(f"### 示例 {i}: {example['hanzi']} (U+{example['unicode']})")
            report_lines.append("")
            report_lines.append(f"- **总反切数**: {example['total_fanqie']}")
            report_lines.append(f"- **完整反切**: {', '.join(example['complete_fanqie']) if example['complete_fanqie'] else '无'}")
            report_lines.append("- **不完整反切**:")

            for incomplete in example['incomplete_fanqie']:
                sources_str = '+'.join(incomplete['sources'])
                report_lines.append(f"  - `{incomplete['fanqie']}` (仅在 {sources_str} 中存在)")

            report_lines.append("")
            report_lines.append("- **各源反切分布**:")
            for source, fanqie_list in example['fanqie_by_source'].items():
                report_lines.append(f"  - **{source.upper()}**: {', '.join(fanqie_list) if fanqie_list else '无'}")

            report_lines.append("")

        # 写入文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))

            logger.info(f"分析报告已生成: {output_file}")
            return output_file

        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析字形完整但反切读音不完整的情况')
    parser.add_argument('--limit', type=int, help='分析记录数限制，用于测试')
    parser.add_argument('--output', '-o', type=str, help='输出报告文件名')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        with IncompleteFanqieAnalyzer() as analyzer:
            # 执行分析
            analyzer.analyze_all_data(limit=args.limit)

            # 生成报告
            report_file = analyzer.generate_markdown_report(args.output)

            if report_file:
                logger.info(f"分析完成！报告已保存到: {report_file}")
            else:
                logger.error("报告生成失败")

    except KeyboardInterrupt:
        logger.info("用户中断分析")
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
